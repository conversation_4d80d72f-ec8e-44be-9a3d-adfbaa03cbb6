<?xml version="1.0" encoding="utf-8"?>
<odoo>
<!--    Tree view of custody property-->
    <record id="custody_property_view_tree" model="ir.ui.view">
        <field name="name">custody.property.view.tree</field>
        <field name="model">custody.property</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
            </tree>
        </field>
    </record>
<!--    Form view of custody property-->
    <record id='custody_property_view_form' model='ir.ui.view'>
        <field name="name">custody.property.view.form</field>
        <field name="model">custody.property</field>
        <field name="arch" type="xml">
            <form string="Equipments">
                <sheet>
                    <field name="image" widget='image' class="oe_avatar"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="property_selection"
                                   widget="radio"/>
                            <field name="product_id"
                                   invisible="property_selection != 'product'"/>
                            <field name="stock_quant_ids"
                                   widget="many2many_tags"/>
                            <field name="company_id"
                                   options="{'no_create': True}"
                                   groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="location_id"
                                   invisible="property_selection != 'product'"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="desc" string="Description">
                            <field name="desc"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
<!--    Action for custody property-->
    <record id="custody_property_action" model="ir.actions.act_window">
        <field name="name">Property</field>
        <field name="res_model">custody.property</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                Click to Create a New Record.
            </p>
        </field>
    </record>
<!--    Menu for hr property-->
    <menuitem action="custody_property_action"
                  id="custody_property_menu"
                  parent="ent_hr_custody.hr_custody_main_menu"
                  name="Property" sequence="5" groups="hr.group_hr_manager"/>
</odoo>
