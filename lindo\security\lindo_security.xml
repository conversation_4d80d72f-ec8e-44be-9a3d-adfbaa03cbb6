<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- <PERSON><PERSON> Hiding Security Group -->
        <record id="group_lindo_hiding" model="res.groups">
            <field name="name"><PERSON><PERSON> Hiding</field>
            <field name="implied_ids" eval="[(4, ref('base.group_no_one'))]"/>
            <field name="comment">Special group for Lindo module with restricted access, automatically assigned to all new users.</field>
        </record>
    </data>
</odoo>
