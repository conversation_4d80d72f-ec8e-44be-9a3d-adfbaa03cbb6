<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="action_account_asset_report_pivot" model="ir.ui.view">
        <field name="name">asset.asset.report.pivot</field>
        <field name="model">asset.asset.report</field>
        <field name="arch" type="xml">
            <pivot string="Assets Analysis" disable_linking="True">
                <field name="asset_category_id" type="row"/>
                <field name="gross_value" type="measure"/>
                <field name="unposted_value" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="action_account_asset_report_graph" model="ir.ui.view">
        <field name="name">asset.asset.report.graph</field>
        <field name="model">asset.asset.report</field>
        <field name="arch" type="xml">
            <graph string="Assets Analysis">
                <field name="asset_category_id" type="row"/>
                <field name="gross_value" type="measure"/>
                <field name="unposted_value" type="measure"/>
            </graph>
        </field>
    </record>
 
    <record id="view_asset_asset_report_search" model="ir.ui.view">
        <field name="name">asset.asset.report.search</field>
        <field name="model">asset.asset.report</field>
        <field name="arch" type="xml">
            <search string="Assets Analysis">
                <field name="date"/>
                <field name="depreciation_date"/>
                <filter string="Draft" name="draft" domain="[('state','=','draft')]"
                        help="Assets in draft state"/>
                <filter string="Running" name="running" domain="[('state','=','open')]"
                        help="Assets in running state"/>
                <separator/>
                <filter string="Posted" name="posted" domain="[('move_check','=',True)]"
                        help="Posted depreciation lines" context="{'unposted_value_visible': 0}"/>
                <field name="asset_id"/>
                <field name="asset_category_id"/>
                <group expand="0" string="Extended Filters...">
                    <field name="partner_id" filter_domain="[('partner_id','child_of',self)]"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </group>
                <group expand="1" string="Group By">
                    <filter string="Asset" name="asset" context="{'group_by':'asset_id'}"/>
                    <filter string="Asset Category" name="asset_category"
                            context="{'group_by':'asset_category_id'}"/>
                    <filter string="Company" name="company" context="{'group_by':'company_id'}"
                            groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Purchase Month" name="purchase_month" help="Date of asset purchase"
                        context="{'group_by':'date:month'}"/>
                    <filter string="Depreciation Month" name="deprecation_month" help="Date of depreciation"
                        context="{'group_by':'depreciation_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_asset_asset_report" model="ir.actions.act_window">
        <field name="name">Assets Analysis</field>
        <field name="res_model">asset.asset.report</field>
        <field name="view_mode">graph,pivot</field>
        <field name="search_view_id" ref="view_asset_asset_report_search"/>
        <field name="domain">[('asset_category_id.type', '=', 'purchase')]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_empty_folder">
            No content
          </p><p>
            From this report, you can have an overview on all depreciations. The
            search bar can also be used to personalize your assets depreciation reporting.
          </p>
        </field>
    </record>
    
    <menuitem id="menu_action_asset_asset_report"
              name="Assets"
              action="action_asset_asset_report"
              parent="account.account_reports_management_menu"
              sequence="21"/>

</odoo>
