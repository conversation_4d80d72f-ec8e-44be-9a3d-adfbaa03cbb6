# -*- coding: utf-8 -*-

from odoo import models, api


class ResUsers(models.Model):
    """Extend res.users to automatically assign lindo_hiding group to new users"""
    _inherit = 'res.users'

    @api.model_create_multi
    def create(self, vals_list):
        """Override create method to automatically assign lindo_hiding group to new users"""
        users = super(ResUsers, self).create(vals_list)

        # Get the lindo_hiding group
        lindo_hiding_group = self.env.ref('lindo.group_lindo_hiding', raise_if_not_found=False)

        if lindo_hiding_group:
            for user in users:
                # Add the lindo_hiding group to the user
                user.groups_id = [(4, lindo_hiding_group.id)]

        return users


# class lindo(models.Model):
#     _name = 'lindo.lindo'
#     _description = 'lindo.lindo'

#     name = fields.Char()
#     value = fields.Integer()
#     value2 = fields.Float(compute="_value_pc", store=True)
#     description = fields.Text()
#
#     @api.depends('value')
#     def _value_pc(self):
#         for record in self:
#             record.value2 = float(record.value) / 100

