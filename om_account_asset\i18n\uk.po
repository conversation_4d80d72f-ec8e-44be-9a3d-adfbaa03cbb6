# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_asset
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-07-07 07:07+0000\n"
"PO-Revision-Date: 2022-07-07 07:07+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid " (grouped)"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr ""

#. module: om_account_asset
#: model:ir.actions.server,name:om_account_asset.account_asset_cron_ir_actions_server
#: model:ir.cron,cron_name:om_account_asset.account_asset_cron
#: model:ir.cron,name:om_account_asset.account_asset_cron
msgid "Account Asset: Generate asset entries"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr "Дата обліку"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""

#. module: om_account_asset
#. openerp-web
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction
msgid "Action Needed"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__active
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__active
msgid "Active"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "Додаткові параметри"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Amount"
msgstr "Сума"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Сума амортизації"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__account_analytic_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_analytic_id
msgid "Analytic Account"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Рахунок активу"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "Категорія активу"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_end_date
msgid "Asset End Date"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__name
msgid "Asset Name"
msgstr "Назва активу"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_start_date
msgid "Asset Start Date"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__name
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "Тип активу"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_list_normal_purchase
msgid "Asset Category"
msgstr "Типи активів"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "Категорія активу"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr ""

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr ""

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr ""

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_form
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:om_account_asset.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "Активи"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_asset_report
#: model:ir.model,name:om_account_asset.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Аналіз активів"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr ""

#. module: om_account_asset
#: model:ir.ui.menu,name:om_account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__open_asset
msgid "Auto-Confirm Assets"
msgstr "Автоматичне підтвердження активів"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__last_day_period
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__last_day_period
msgid "Based on Last Day of Purchase Period"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Скасувати"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Category"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__close
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__close
msgid "Close"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Closed"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__company_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Company"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method
msgid "Computation Method"
msgstr "Метод обчислення"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "Обчислити амортизацію"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Підтвердити"

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Asset Moves"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Revenue Moves"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr "Накопичена амортизація"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__currency_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__currency_id
#, python-format
msgid "Currency"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Current"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__date
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Date"
msgstr "Дата"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__degressive
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__degressive
msgid "Degressive"
msgstr "Спосіб зменшуваного залишку"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "Амортизація"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "Дошка амортизації"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_nbr
msgid "Depreciation Count"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr "Дата амортизації"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date_first_depreciation
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__date_first_depreciation
msgid "Depreciation Dates"
msgstr "Дати амортизації"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "Записи амортизації: рахунок активу"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "Записи амортизації: рахунок витрат"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "Інформація про амортизацію"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "Метод амортизації"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Depreciation line posted."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__display_name
msgid "Display Name"
msgstr "Відобразити назву"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Document closed."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__draft
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "Чернетка"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__end
msgid "Ending Date"
msgstr "Кінцева дата"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_end
msgid "Ending date"
msgstr "Кінцева дата"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid "First Depreciation Date"
msgstr "Перша дата амортизації"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_follower_ids
msgid "Followers"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_channel_ids
msgid "Followers (Channels)"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""

#. module: om_account_asset
#: model:ir.ui.menu,name:om_account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "Нарахування амортизації та зносу"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Згенерувати записи"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value
msgid "Gross Value"
msgstr "Валова вартість"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Group By..."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr "Журнал групи записів"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__id
msgid "ID"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the asset date (purchase date) instead of the first January / Start "
"date of fiscal year"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_nbr
msgid "Installment Count"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Рахунок"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Items"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__journal_id
msgid "Journal"
msgstr "Журнал"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "Записи журнала"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_account_move____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify____last_update
#: model:ir.model.fields,field_description:om_account_asset.field_product_template____last_update
msgid "Last Modified on"
msgstr "Останні зміни"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__linear
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__linear
msgid "Linear"
msgstr "Лінійний"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr "Пов'язані"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__manual
msgid "Manual"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__manual
msgid "Manual (Defaulted on Purchase Date)"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_ids
msgid "Messages"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify"
msgstr ""

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_modify
#: model:ir.model,name:om_account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify Asset"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid "No content"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__note
msgid "Note"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "Номер амортизації"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__number
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__number
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "Кількість записів"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "Кількість місяців у періоді"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "Один запис кожні"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__partner_id
#, python-format
msgid "Partner"
msgstr "Партнер"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_period
msgid "Period Length"
msgstr "Тривалість періоду"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "Періодичність"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "Нарахування амортизації та зносу"

#. module: om_account_asset
#. openerp-web
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__move_check
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr ""

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_product_template
msgid "Product Template"
msgstr "Шаблон товару"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr "Відповідно до часу"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid ""
"Prorata temporis can be applied only for the \"number of depreciations\" "
"time method."
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__purchase
msgid "Purchase: Asset"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__name
msgid "Reason"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__code
msgid "Reference"
msgstr "Референс"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "Залишок"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "Залишкова вартість"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__open
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__open
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "Діючий"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__sale
msgid "Sale: Revenue Recognition"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Sales"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "Ліквідаційна вартість"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "Пошук категорії активу"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__sequence
msgid "Sequence"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__state
msgid "Status"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"The number of depreciations must be greater than the number of posted or "
"draft entries to allow for complete depreciation of the asset."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be 0."
msgstr "Амортизація за період, обраних ОЗ, не може дорівнювати 0."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be based on the last day of the purchase month or the purchase year (depending on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the purchase date."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be based on the last day of the purchase month or the purchase year (depending on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the purchase date.\n"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid ""
"This depreciation is already linked to a journal entry. Please post or "
"delete it."
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period\n"
"                        of asset/revenue recognition as well."
msgstr ""
"Майстер нарахує знос/амортизацію на обраний місяць.\n"
"Будуть сформовані бухгалтерські проведення для усіх ОЗ введених в експлуатацію на цей період."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_time
msgid "Time Method"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "Часовий метод базується на"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__type
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__type
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Type"
msgstr ""

#. module: om_account_asset
#. openerp-web
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#, python-format
msgid "Unposted"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_unread
msgid "Unread Messages"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Постачальник"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "Vendor bill cancelled."
msgstr "Скасувати"

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "Vendor bill reset to draft."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,help:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__name
msgid "Year"
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted installment lines."
msgstr ""

#. module: om_account_asset
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft for an entry having a posted asset"
msgstr ""

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr "Підтвердити"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "months"
msgstr "місяців"
