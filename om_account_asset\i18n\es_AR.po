# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_asset
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-21 20:43+0000\n"
"PO-Revision-Date: 2024-03-21 20:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr "Asientos de activos"

#. module: om_account_asset
#: model:ir.actions.server,name:om_account_asset.account_asset_cron_ir_actions_server
msgid "Account Asset: Generate asset entries"
msgstr "Cuenta de Activos: Generar asientos de activos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr "Fecha de la cuenta"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Cuenta utilizada en los asientos de depreciación, para disminuir el valor "
"del activo"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Cuenta utilizada en los asientos periódicos, para registrar una parte del "
"activo como gasto."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""
"Cuenta utilizada para registrar la compra del activo a su precio original."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__active
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__active
msgid "Active"
msgstr "Activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_exception_decoration
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Excepción de actividad"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_state
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_state
msgid "Activity State"
msgstr "Estado de Actividad"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_type_icon
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de Tipo de actividad"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "Opciones adicionales"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "Importe de las líneas de depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr "Importe de las líneas de cuotas"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__account_analytic_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_analytic_id
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__analytic_distribution
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribución analítica"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__analytic_distribution_search
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "Búsqueda de distribución analítica"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__analytic_precision
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__analytic_precision
msgid "Analytic Precision"
msgstr "Precisión analítica"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "Activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Cuenta de activos"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_category_id
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "Tipos de activo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "Duraciones de activos para modificar"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_end_date
msgid "Asset End Date"
msgstr "Fecha de finalización del activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr "Método de tiempo del activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__name
msgid "Asset Name"
msgstr "Nombre del activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_start_date
msgid "Asset Start Date"
msgstr "Fecha de inicio del activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__name
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr "Tipo de activo"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "Categoría de activos"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "Línea de depreciación de activos"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "Reconocimiento de Activos/Ingresos"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_form
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:om_account_asset.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "Activos"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_asset_report
#: model:ir.model,name:om_account_asset.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "Análisis de activos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "Líneas de depreciación de activos"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Activos en estado cerrado"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Activos en estado borrador y abierto"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "Activos en estado borrador"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "Activos en estado en curso"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_attachment_count
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_attachment_count
msgid "Attachment Count"
msgstr "Cantidad de adjuntos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__open_asset
msgid "Auto-Confirm Assets"
msgstr "Confirmación automática de activos"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__last_day_period
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__last_day_period
msgid "Based on Last Day of Purchase Period"
msgstr "Basado en el último día del período de compra"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "Cancelar"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Category"
msgstr "Categoría"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "Categoría de activo"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"Marque esto si desea confirmar automáticamente los activos de esta categoría"
" cuando se crean mediante facturas."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr "Marque esto si desea agrupar las entradas generadas por categorías."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"Elija el método a usar para calcular la cantidad de líneas de depreciación.\n"
"  * Lineal: Calculado en base a: Valor Bruto / Número de Depreciaciones.\n"
"  * Decreciente: Calculado en base a: Valor Residual * Factor Decreciente"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the depreciations won't go beyond."
msgstr ""
"Elija el método a utilizar para calcular las fechas y el número de asientos.\n"
"  * Número de Asientos: Fija el número de entradas y el tiempo entre 2 depreciaciones.\n"
"  * Fecha de Finalización: Elija el tiempo entre 2 depreciaciones y la fecha en que las depreciaciones no se extenderán."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr ""
"Elija el período para el que desea registrar automáticamente las líneas de "
"depreciación de los activos en curso"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__close
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__close
msgid "Close"
msgstr "Cerrar"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Closed"
msgstr "Cerrado"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__company_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "Compañía"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method
msgid "Computation Method"
msgstr "Método de cálculo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "Computar activo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "Calcular depreciación"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "Confirmar"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Creado en"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr "Depreciación acumulada"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__currency_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Current"
msgstr "Corriente"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr "Depreciación corriente"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__date
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Date"
msgstr "Fecha"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "Fecha del activo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "Fecha de compra del activo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "Fecha de depreciación"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "Cuenta de Ingresos diferidos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "Tipos de Ingresos diferidos"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "Ingresos diferidos"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__degressive
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__degressive
msgid "Degressive"
msgstr "Decreciente"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr "Factor decreciente"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "Depreciación"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "Tabla de depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_nbr
msgid "Depreciation Count"
msgstr "Recuento de depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr "Fecha de depreciación "

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date_first_depreciation
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__date_first_depreciation
msgid "Depreciation Dates"
msgstr "Fechas de depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "Asientos de depreciación: Cuenta de activos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "Asientos de depreciación: Cuenta de gastos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr "Asiento de depreciación"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "Información de depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "Líneas de depreciación"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "Método de depreciación"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "Mes de la depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr "Nombre de la depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__draft
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "Borrador"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__end
msgid "Ending Date"
msgstr "Fecha de finalización"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_end
msgid "Ending date"
msgstr "Fecha de finalización"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "Filtros extendidos..."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid "First Depreciation Date"
msgstr "Primera fecha de depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_follower_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_partner_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Empresas)"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_type_icon
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets depreciation reporting."
msgstr ""
"Desde este informe, puede tener una visión general de todas las depreciaciones. \n"
"La barra de búsqueda también se puede utilizar para personalizar sus informes."

#. module: om_account_asset
#: model:ir.ui.menu,name:om_account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "Generar asientos de activos"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "Generar asientos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr "Importe bruto"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value
msgid "Gross Value"
msgstr "Valor bruto"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "Valor bruto del activo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "Agrupar por"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Agrupar por..."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr "Diario de grupo de asientos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__has_message
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__id
msgid "ID"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_exception_icon
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_exception_icon
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, los mensajes nuevos requieren su atención."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_sms_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_has_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcado, algunos mensajes tienen un error de entrega."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the asset date (purchase date) instead of the first January / Start "
"date of fiscal year"
msgstr ""
"Indica que el primer asiento de depreciación para este activo debe "
"realizarse a partir de la fecha del activo (fecha de compra) en lugar del 1 "
"de Enero / fecha de inicio del año fiscal"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr ""
"Indica que el primer asiento de depreciación para este activo debe "
"realizarse a partir de la fecha de compra en lugar del 1 de Enero"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_nbr
msgid "Installment Count"
msgstr "Cantidad de cuotas"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "Factura"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_is_follower
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_is_follower
msgid "Is Follower"
msgstr "Es seguidor"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Es la cantidad que planea tener que no puede depreciar."

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Items"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__journal_id
msgid "Journal"
msgstr "Diario"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Journal Entries"
msgstr "Asientos contables"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Item del Diario"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__linear
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__linear
msgid "Linear"
msgstr "Lineal"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr "Vinculado"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__manual
msgid "Manual"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__manual
msgid "Manual (Defaulted on Purchase Date)"
msgstr "Manual (Por defecto en la fecha de compra)"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_has_error
msgid "Message Delivery error"
msgstr "Mensaje de error de entrega"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify"
msgstr "Modificar"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_modify
#: model:ir.model,name:om_account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Modificar activo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "Modificar depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "Ingresos recurrentes mensuales"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__my_activity_date_deadline
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_date_deadline
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite para la próxima actividad"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_summary
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la próxima actividad"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_type_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr "Próximo período de depreciación"

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid "No content"
msgstr "Sin contenido"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__note
msgid "Note"
msgstr "Nota"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"Nótese que esta fecha no altera el cómputo del primer asiento de diario en "
"caso de activos prorata temporis. Simplemente cambia su fecha contable"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction_counter
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "Número de depreciaciones"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__number
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__number
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "Número de asientos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "Número de meses en un período"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error_counter
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction_counter
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error_counter
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de entrega"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "Una entrada cada"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__partner_id
msgid "Partner"
msgstr "Empresa"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_period
msgid "Period Length"
msgstr "Duración del período"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "Periodicidad"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "Líneas posteriores a la depreciación"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__move_check
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Posted"
msgstr "Publicado"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr "Importe publicado"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "Líneas de depreciación publicadas"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_product_template
msgid "Product"
msgstr "Producto"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "Compra"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "Mes de compra"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__purchase
msgid "Purchase: Asset"
msgstr "Compra: Activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__rating_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__rating_ids
msgid "Ratings"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__name
msgid "Reason"
msgstr "Razón"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr "Cuenta de reconocimiento"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "Cuenta de reconocimiento de ingresos"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__code
msgid "Reference"
msgstr "Referencia"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr ""

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "Valor residual"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_user_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__open
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__open
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "En curso"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_sms_error
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega SMS"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__sale
msgid "Sale: Revenue Recognition"
msgstr "Venta: Reconocimiento de ingresos"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "Ventas"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "Valor de rescate"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "Buscar categoría de activos"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "Vender o desechar"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "Establecer en borrador"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "Indique aquí el tiempo entre 2 depreciaciones, en meses"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr "Estado del activo"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__state
msgid "Status"
msgstr "Estado"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_state
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencido: la fecha de vencimiento ya ha pasado\n"
"Hoy: la fecha de la actividad es hoy.\n"
"Planificado: actividades futuras."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr "La cantidad de tiempo entre dos depreciaciones, en meses"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "El número de depreciaciones necesarias para depreciar su activo"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be based on the last day of the purchase month or the purchase year (depending on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the purchase date."
msgstr ""
"La forma de calcular la fecha de la primera depreciación.\n"
"  * Basado en el último día del período de compra: Las fechas de depreciación se basarán en el último día del mes de compra o del año de compra (dependiendo de la periodicidad de las depreciaciones).\n"
"  * Basado en la fecha de compra: Las fechas de depreciación se basarán en la fecha de compra."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be based on the last day of the purchase month or the purchase year (depending on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the purchase date.\n"
msgstr ""
"La forma de calcular la fecha de la primera depreciación.\n"
"  * Basado en el último día del período de compra: Las fechas de depreciación se basarán en el último día del mes de compra o del año de compra (dependiendo de la periodicidad de las depreciaciones).\n"
"  * Basado en la fecha de compra: Las fechas de depreciación se basarán en la fecha de compra."

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month.<br/>\n"
"                        This will generate journal entries for all related installment lines on this period\n"
"                        of asset/revenue recognition as well."
msgstr ""
"Este asistente publicará líneas de cuotas/depreciación para el mes seleccionado.<br/>\n"
"                        Esto también generará asientos de diario para todas las líneas de pago relacionadas\n"
"                        en este período de reconocimiento de activos/ingresos."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_time
msgid "Time Method"
msgstr "Método de tiempo"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "Método de tiempo basado en"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__type
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__type
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Type"
msgstr "Tipo"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_exception_decoration
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr "Importe no contabilizado"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "Proveedor"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__website_message_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__website_message_ids
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,help:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"Cuando se crea una activo, el estado es 'Borrador'.\n"
"Si se confirma el activo, el estado pasa a 'En curso' y las líneas de depreciación se pueden contabilizar en la contabilidad.\n"
"Puede cerrar manualmente un activo cuando termine la depreciación. Si se registra la última línea de depreciación, el activo pasa automáticamente a ese estado."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__name
msgid "Year"
msgstr "Año"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr ""

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "p. ej. Computadoras"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "p. ej. Laptop iBook"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "months"
msgstr "meses"
