"id","name","model_id:id","group_id:id","perm_read","perm_write","perm_create","perm_unlink"
"access_hr_custody_hr","hr.custody.hr","model_hr_custody","hr.group_hr_manager",1,1,1,1
"access_hr_custody_hr_manager","hr.custody.user","model_hr_custody","hr.group_hr_user",1,1,1,1
"access_hr_custody_hr_employee","hr.custody.employee","model_hr_custody","base.group_user",1,1,1,0
"access_hr_custody_hr1","hr.custody.hr1","model_custody_property","hr.group_hr_manager",1,1,1,1
"access_hr_custody_hr_manager1","hr.custody.user1","model_custody_property","hr.group_hr_user",1,1,1,1
"access_hr_custody_hr_employee1","hr.custody.employee1","model_custody_property","base.group_user",1,0,0,0
"access_hr_custody_hr2","hr.custody.hr2","model_contract_renewal","hr.group_hr_manager",1,1,1,1
"access_hr_custody_hr3","hr.custody.hr3","model_report_custody","hr.group_hr_manager",1,1,1,1
"access_hr_custody_hr_manager2","hr.custody.user2","model_contract_renewal","hr.group_hr_user",1,1,1,1
"access_hr_custody_hr_manager3","hr.custody.user3","model_report_custody","hr.group_hr_user",1,1,1,1
"access_hr_custody_hr_employee2","hr.custody.employee2","model_contract_renewal","base.group_user",1,1,1,0
"access_hr_custody_reason","hr.custody.reason","model_custody_refuse","hr.group_hr_manager",1,1,1,1
