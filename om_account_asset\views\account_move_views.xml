<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_invoice_asset_category" model="ir.ui.view">
        <field name="name">account.move.supplier.form</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='account_id']" position="before">
                <!--   todo                    column_invisible="parent.move_type not in ['in_invoice]"-->
                <field string="Asset Category" name="asset_category_id" force_save="1"
                       domain="[('type','=','purchase')]" context="{'default_type':'purchase'}"/>
            </xpath>
            <xpath expr="//field[@name='line_ids']/tree/field[@name='account_id']" position="before">
                <field string="Asset Category" name="asset_category_id" invisible="1"/>
            </xpath>
        </field>
    </record>

</odoo>
