<?xml version="1.0" encoding="utf-8"?>
<odoo>
<!--    Sequence for hr custody-->
    <record id="seq_hr_custody_seq" model="ir.sequence">
        <field name="name">Custody Code</field>
        <field name="code">hr.custody</field>
        <field name="prefix">CR</field>
        <field eval="4" name="padding"/>
        <field eval="False" name="company_id"/>
    </record>
    <!--    Form views for wizard reason-->
    <record id='custody_refuse_view_form' model='ir.ui.view'>
        <field name="name">custody.refuse.view.form</field>
        <field name="model">custody.refuse</field>
        <field name="arch" type="xml">
            <form string="Update Reason">
                <group>
                    <field name="reason" required="True"/>
                </group>
                <footer>
                    <button name="send_reason" string="Proceed" type="object"
                            class="oe_highlight"/>
                    or
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <!--    Action-->
    <record id="custody_refuse_action" model='ir.actions.act_window'>
        <field name="name">Update Reason</field>
        <field name="res_model">custody.refuse</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="custody_refuse_view_form"/>
        <field name="target">new</field>
    </record>
    <record id='contract_renewal_view_form' model='ir.ui.view'>
        <field name="name">contract.renewal.view.form</field>
        <field name="model">contract.renewal</field>
        <field name="arch" type="xml">
            <form string="Renewal Request">
                <group>
                    <group>
                        <field name="returned_date"/>
                    </group>
                </group>
                <footer>
                    <button name="proceed" string="Proceed" type="object"
                            class="oe_highlight"/>
                    or
                    <button string="Cancel" class="oe_link"
                            special="cancel"/>
                </footer>
            </form>
        </field>
    </record>
    <record model='ir.actions.act_window' id='contract_renewal_action'>
        <field name="name">Custody Request</field>
        <field name="res_model">contract.renewal</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="contract_renewal_view_form"/>
        <field name="target">new</field>
    </record>
<!--    Form views for hr custody-->
    <record id="hr_custody_view_form" model="ir.ui.view">
        <field name="name">hr.custody.view.form</field>
        <field name="model">hr.custody</field>
        <field name="arch" type="xml">
            <form string="Custody">
                <header>
                    <button string="Send For Approval" type="object"
                            name="sent"
                            class="oe_highlight"
                            invisible="state not in ('draft')"/>
                    <button string="Approve" groups="hr.group_hr_manager"
                            type="object" name="approve"
                            class="oe_highlight"
                            invisible="state not in ('to_approve') or renew_return_date == True"/>
                    <button string="Renewal Approval"
                            groups="hr.group_hr_manager" type="object"
                            name="renew_approve"
                            class="oe_highlight"
                            invisible="state not in ('to_approve') or renew_return_date == False"/>
                    <button string="Refuse" groups="hr.group_hr_manager"
                            type="action"
                            name="%(ent_hr_custody.custody_refuse_action)d"
                            invisible="state not in ('to_approve') or renew_return_date == True"
                            class="btn-primary"
                            context="{'reject_id':id,'model_id':'hr.custody'}"/>
                    <button class="btn-primary"
                            name="%(ent_hr_custody.custody_refuse_action)d"
                            string="Refuse"
                            groups="hr.group_hr_manager" type="action"
                            invisible="state not in ('to_approve') or renew_return_date == False"
                            context="{'reject_id':id,'model_id':'hr.custody','renew': 'renew'}"/>
                    <button string="Set to Draft" type="object"
                            name="set_to_draft"
                            invisible="state not in ('rejected')"/>
                    <button string="Return" type="object"
                            name="set_to_return"
                            groups="hr.group_hr_manager"
                            invisible="state not in ('approved')"/>
                    <button string="Send Mail" type="object"
                            name="send_mail" groups="hr.group_hr_manager"
                            invisible="state not in ('approved') or mail_send == False"/>
                    <button string="Send Mail" type="object"
                            class="oe_highlight" name="send_mail"
                            groups="hr.group_hr_manager"
                            invisible="state not in ('approved') or mail_send == True"/>
                    <button string="Renew" type="action"
                            name="%(contract_renewal_action)d"
                            context="{'custody_id':id}" class="oe_highlight"
                            invisible="state not in ('approved') or renew_return_date == True"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,to_approve,approved,returned"/>
                </header>
                <sheet>
                    <h1>
                        <field name="name" readonly="1" nolabel="1"/>
                    </h1>
                    <group>
                        <group>
                            <field name="read_only" invisible="1"/>
                            <field name="employee"
                                   readonly="read_only == False"
                            />
                            <field name="custody_name"
                                   options="{'no_open':True,'no_create':True}"
                                   groups="base.group_multi_company"/>
                            <field name="property_type" invisible="1"/>
                            <field name="quantity"
                                   invisible="property_type != True"/>
                            <field name="location"
                                   invisible="property_type != True"/>
                            <field name="renew_return_date" invisible="1"/>
                            <field name="renew_reject" invisible="1"/>
                            <field name="mail_send" invisible="1"/>
                            <field name="purpose"/>
                        </group>
                        <group>
                            <field name="date_request"/>
                            <field name="return_date"
                                   invisible="renew_return_date == True and renew_date not in [None,False] and renew_reject == False"
                            />
                            <field name="renew_date"
                                   invisible="renew_return_date == False or state != 'to_approve'"
                            />
                            <field name="rejected_reason"
                                   invisible="state not in ['rejected']"
                            />
                            <field name="renew_rejected_reason"
                                   invisible="renew_reject == False or state not in ('approved')"/>
                            <field name="company_id"
                                   options="{'no_create': True}"
                                   groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Notes">
                            <field name="notes"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"
                           widget="mail_followers"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>
<!--    Tree views for hr custody-->
    <record id="hr_custody_tree_view" model="ir.ui.view">
        <field name="name">hr.custody.tree</field>
        <field name="model">hr.custody</field>
        <field name="arch" type="xml">
            <tree decoration-info="state == 'draft'">
                <field name="name"/>
                <field name="employee"/>
                <field name="custody_name"/>
                <field name="purpose"/>
                <field name="date_request"/>
                <field name="return_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>
    <record id="hr_custody_search_view" model="ir.ui.view">
        <field name="name">hr.custody.search</field>
        <field name="model">hr.custody</field>
        <field name="arch" type="xml">
            <search string="Custody">
                <field name="name"/>
                <field name="employee"/>
                <field name="custody_name"/>
                <field name="purpose"/>
                <field name="date_request"/>
                <field name="return_date"/>
                <field name="state"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="status" domain="[]"
                            context="{'group_by':'state'}"/>
                    <filter string="Employee" name="employee" domain="[]"
                            context="{'group_by':'employee'}"/>
                    <filter string="Custody Name" name="custody" domain="[]"
                            context="{'group_by':'custody_name'}"/>
                </group>
            </search>
        </field>
    </record>
<!--    Action-->
    <record id="action_hr_custody" model="ir.actions.act_window">
        <field name="name">Custody</field>
        <field name="res_model">hr.custody</field>
        <field name="view_mode">tree,form</field>
        <field name="search_view_id" ref="hr_custody_search_view"/>
        <field name="help" type="html">
            <p class="oe_view_nocontent_create">
                Click to Create a New Record.
            </p>
        </field>
    </record>
<!--    Menu items-->
    <menuitem id="hr_custody_main_menu"
              web_icon="ent_hr_custody,static/description/icon.png"
              name="Custody" sequence="20"/>

    <menuitem id="hr_custody_menu" parent="hr_custody_main_menu"
              name="Custody Management" sequence="20"/>

    <menuitem id="hr_custody_menu" action="action_hr_custody"
              parent="hr_custody_main_menu"
              name="Custody Request" sequence="1"/>
</odoo>
