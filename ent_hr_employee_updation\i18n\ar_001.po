# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* ent_hr_employee_updation
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-02-12 03:21+0000\n"
"PO-Revision-Date: 2020-02-12 03:21+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__id_attachment_ids
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__passport_attachment_ids
msgid "Attachment"
msgstr "المرفق"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__relation
msgid "Contact"
msgstr "اتصل"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__member_contact
msgid "Contact No"
msgstr "رقم الاتصال"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_emergency_contact__number
msgid "Contact Number"
msgstr "رقم الاتصال"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__create_uid
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__create_uid
msgid "Created by"
msgstr "انشأ من قبل"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__create_date
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__create_date
msgid "Created on"
msgstr "تم إنشاؤها على"

#. module: ent_hr_employee_updation
#: model:ir.model.fields.selection,name:ent_hr_employee_updation.selection__hr_employee_family__relation__daughter
msgid "Daughter"
msgstr "ابنة"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__display_name
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__display_name
msgid "Display Name"
msgstr "اسم العرض"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__emergency_contacts
msgid "Emergency Contact"
msgstr "الاتصال بالطوارىء"

#. module: ent_hr_employee_updation
#: model_terms:ir.ui.view,arch_db:ent_hr_employee_updation.hr_employee_inherit_form_view
msgid "Emergency Contacts"
msgstr "جهة اتصال للطوارئ"

#. module: ent_hr_employee_updation
#: model:ir.model,name:ent_hr_employee_updation.model_hr_employee
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__employee_id
msgid "Employee"
msgstr "الموظف"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__employee_obj
msgid "Employee Obj"
msgstr "كائن الموظف"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__id_expiry_date
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__passport_expiry_date
msgid "Expiry Date"
msgstr "تاريخ الانتهاء"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee__id_expiry_date
msgid "Expiry date of Identification ID"
msgstr "تاريخ انتهاء معرف الهوية"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee__passport_expiry_date
msgid "Expiry date of Passport ID"
msgstr "تاريخ انتهاء صلاحية جواز السفر"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__fam_ids
#: model_terms:ir.ui.view,arch_db:ent_hr_employee_updation.hr_employee_inherit_form_view
msgid "Family"
msgstr "عائلة"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee__fam_ids
#: model_terms:ir.ui.view,arch_db:ent_hr_employee_updation.hr_employee_inherit_form_view
msgid "Family Information"
msgstr "معلومات العائلة"

#. module: ent_hr_employee_updation
#: model:ir.model.fields.selection,name:ent_hr_employee_updation.selection__hr_employee_family__relation__father
msgid "Father"
msgstr "الآب"

#. module: ent_hr_employee_updation
#: model:ir.model,name:ent_hr_employee_updation.model_hr_emergency_contact
msgid "HR Emergency Contact"
msgstr "الموارد البشرية في حالات الطوارئ الاتصال"

#. module: ent_hr_employee_updation
#: model:ir.actions.server,name:ent_hr_employee_updation.employee_data_reminder_ir_actions_server
#: model:ir.cron,cron_name:ent_hr_employee_updation.employee_data_reminder
#: model:ir.cron,name:ent_hr_employee_updation.employee_data_reminder
msgid "HR Employee Data Expiration"
msgstr "موظف بيانات الموارد البشرية انتهاء الصلاحية"

#. module: ent_hr_employee_updation
#: model:ir.model,name:ent_hr_employee_updation.model_hr_employee_family
msgid "HR Employee Family"
msgstr "عائلة موظف الموارد البشرية"

#. module: ent_hr_employee_updation
#: model:ir.ui.menu,name:ent_hr_employee_updation.hr_management_menu
msgid "HR Management"
msgstr "إدارة الموارد البشرية"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__id
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__id
msgid "ID"
msgstr "هوية شخصية"

#. module: ent_hr_employee_updation
#: code:addons/ent_hr_employee_updation/models/hr_employee.py:0
#, python-format
msgid "ID-%s Expired On %s"
msgstr "انتهت صلاحية معرف ٪ s في ٪ s"

#. module: ent_hr_employee_updation
#: model_terms:ir.ui.view,arch_db:ent_hr_employee_updation.hr_employee_inherit_form_view
msgid "Identification ID"
msgstr "معرف الهوية"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__joining_date
msgid "Joining Date"
msgstr "تاريخ الانضمام"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact____last_update
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__write_uid
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__write_date
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee__personal_mobile
msgid "Mobile"
msgstr "التليفون المحمول"

#. module: ent_hr_employee_updation
#: model:ir.model.fields.selection,name:ent_hr_employee_updation.selection__hr_employee_family__relation__mother
msgid "Mother"
msgstr "أم"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__member_name
msgid "Name"
msgstr "اسم"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_emergency_contact__number
msgid "Number"
msgstr "رقم"

#. module: ent_hr_employee_updation
#: model_terms:ir.ui.view,arch_db:ent_hr_employee_updation.hr_employee_inherit_form_view
msgid "Passport ID"
msgstr "رقم جواز السفر"

#. module: ent_hr_employee_updation
#: code:addons/ent_hr_employee_updation/models/hr_employee.py:0
#, python-format
msgid "Passport-%s Expired On %s"
msgstr "انتهت صلاحية جواز السفر ٪ s في ٪ s"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_emergency_contact__relation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee_family__relation
msgid "Relation with employee"
msgstr "العلاقة مع الموظف"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,field_description:ent_hr_employee_updation.field_hr_employee_family__relation
msgid "Relationship"
msgstr "صلة"

#. module: ent_hr_employee_updation
#: model:ir.ui.menu,name:ent_hr_employee_updation.employee_report_menu
msgid "Reports"
msgstr "تقارير"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee_family__employee_id
msgid "Select corresponding Employee"
msgstr "حدد الموظف المقابل"

#. module: ent_hr_employee_updation
#: model:ir.model.fields.selection,name:ent_hr_employee_updation.selection__hr_employee_family__relation__son
msgid "Son"
msgstr "ابن"

#. module: ent_hr_employee_updation
#: model:ir.model.fields.selection,name:ent_hr_employee_updation.selection__hr_employee_family__relation__wife
msgid "Wife"
msgstr "زوجة"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee__passport_attachment_ids
msgid "You can attach the copy of Passport"
msgstr "يمكنك إرفاق نسخة من جواز السفر"

#. module: ent_hr_employee_updation
#: model:ir.model.fields,help:ent_hr_employee_updation.field_hr_employee__id_attachment_ids
msgid "You can attach the copy of your Id"
msgstr "يمكنك إرفاق نسخة من هويتك"
