# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account_community
# 
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: JH CHOI <<EMAIL>>, 2019\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_account_community
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr "회계"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "회계 항목"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:114
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:129
#, python-format
msgid "Adjustment Entry"
msgstr "정리기장"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "분석 계정"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_credit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "대변 계정"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__date
msgid "Date Account"
msgstr "정산일"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_debit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "차변 계정"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_contract
msgid "Employee Contract"
msgstr "근로계약서"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "선택한 직원의 급여명세서 일괄 생성"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "유효일(급여명세서) 기간을 비워두십시오."

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip
msgid "Pay Slip"
msgstr "급여명세서"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "급여명세서 일괄 처리"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_line
msgid "Payslip Line"
msgstr "급여명세서 명세"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:65
#, python-format
msgid "Payslip of %s"
msgstr "%s 급여명세서"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_run__journal_id
msgid "Salary Journal"
msgstr "급여 분개장"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_salary_rule
msgid "Salary Rule"
msgstr "급여 규칙"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_tax_id
msgid "Tax"
msgstr "세금"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:112
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr "경비 분개장의 \"%s\" 대변 계정이 올바르게 구성되지 않았습니다!"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:127
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr "경비 분개장의 \"%s\" 대변 계정이 올바르게 구성되지 않았습니다!"
