# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account_community
# 
# Translators:
# <PERSON>, 2018
# MS, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-21 13:17+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Portuguese (https://www.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account_community
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_community.hr_salary_rule_form_inherit
msgid "Accounting"
msgstr "Contabilidade"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "Entrada Contabilística"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:114
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:129
#, python-format
msgid "Adjustment Entry"
msgstr "Entrada de Ajuste"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Conta Analítica"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_credit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Conta de Crédito"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__date
msgid "Date Account"
msgstr "Data da conta"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_debit
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Conta de Débito"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_contract
msgid "Employee Contract"
msgstr "Contrato do Funcionário"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Gerar recibos de vencimento para todos os funcionários selecionados"

#. module: hr_payroll_account_community
#: model:ir.model.fields,help:hr_payroll_account_community.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""
"Mantenha vazio para usar no período da data de validação(Recibo de "
"Vencimento)."

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip
msgid "Pay Slip"
msgstr "Recibo de Salário"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lotes de Recibos de Vencimento"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_payslip_line
msgid "Payslip Line"
msgstr "Linha Recibo de Vencimento"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:65
#, python-format
msgid "Payslip of %s"
msgstr "Recibo de vencimento de %s"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_contract__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_run__journal_id
msgid "Salary Journal"
msgstr "Diário salarial"

#. module: hr_payroll_account_community
#: model:ir.model,name:hr_payroll_account_community.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Regras de Salários"

#. module: hr_payroll_account_community
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_payslip_line__account_tax_id
#: model:ir.model.fields,field_description:hr_payroll_account_community.field_hr_salary_rule__account_tax_id
msgid "Tax"
msgstr "Imposto"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:112
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Credit Account!"
msgstr ""
"O Diário de Despesas \"%s\" não foi configurado corretamente na Conta de "
"Crédito!"

#. module: hr_payroll_account_community
#: code:addons/hr_payroll_account_community/models/hr_payroll_account_community.py:127
#, python-format
msgid "The Expense Journal \"%s\" has not properly configured the Debit Account!"
msgstr ""
"O Diário de Despesas \"%s\" não foi configurado corretamente na Conta de "
"Débito"
