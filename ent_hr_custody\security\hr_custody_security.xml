<?xml version="1.0" ?>
<odoo>
<!--    Rule for multi companies: model hr custody -->
    <record id="property_rule_custody_req" model="ir.rule">
        <field name="name">Custody Request Multi Company</field>
        <field name="model_id" ref="model_hr_custody"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>
<!--    Rule for manger : model hr custody-->
    <record id="hr_custody_personal_rule_manager" model="ir.rule">
        <field name="name">Employee Resignation Manager</field>
        <field name="model_id" ref="model_hr_custody"/>
        <field name="domain_force">[('employee.user_id.id', '=', user.id)]</field>
        <field name="groups" eval="[(4, ref('hr.group_hr_user'))]"/>
    </record>
<!--    Rule for user: model hr custody-->
    <record id="hr_custody_personal_rule_user" model="ir.rule">
        <field name="name">Employee Resignation user</field>
        <field name="model_id" ref="model_hr_custody"/>
        <field name="domain_force">[(1, '=',1)]</field>
        <field name="groups" eval="[(4, ref('hr.group_hr_manager'))]"/>
    </record>
</odoo>
