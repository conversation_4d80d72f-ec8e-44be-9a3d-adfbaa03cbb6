# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* om_account_asset
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0-********\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-23 21:29+0000\n"
"PO-Revision-Date: 2023-11-24 06:09+0800\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 3.4.1\n"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid " (copy)"
msgstr " (副本)"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid " (grouped)"
msgstr " （分組）"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__entry_count
msgid "# Asset Entries"
msgstr "#資產項目"

#. module: om_account_asset
#: model:ir.actions.server,name:om_account_asset.account_asset_cron_ir_actions_server
msgid "Account Asset: Generate asset entries"
msgstr "資產管理:生成資產條目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid "Account Date"
msgstr "帳戶日期"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr "在折舊條目中使用的帳戶，以減少資產價值。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr "在定期專案中使用的帳戶，將資產的一部分記錄為費用。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__account_asset_id
msgid "Account used to record the purchase of the asset at its original price."
msgstr "會計用於記錄資產採購的原始價格。"

#. module: om_account_asset
#. odoo-javascript
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#, python-format
msgid "Accounting entries waiting for manual verification"
msgstr "等待人工驗證的會計分錄"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_needaction
msgid "Action Needed"
msgstr "需採取行動"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__active
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__active
msgid "Active"
msgstr "啟用"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_ids
msgid "Activities"
msgstr "活動"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_exception_decoration
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_state
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_type_icon
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Additional Options"
msgstr "附加選項"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Amount"
msgstr "金額"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_value
msgid "Amount of Depreciation Lines"
msgstr "折舊明細金額"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_value
msgid "Amount of Installment Lines"
msgstr "遞延收入明細數量"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__account_analytic_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_analytic_id
msgid "Analytic Account"
msgstr "分析科目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__analytic_distribution
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__analytic_distribution
msgid "Analytic Distribution"
msgstr "分析分攤"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__analytic_distribution_search
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr "分析分佈搜索"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__analytic_precision
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__analytic_precision
msgid "Analytic Precision"
msgstr "分析精度"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__asset_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Asset"
msgstr "資產"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_asset_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "資產項目"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_list_normal_purchase
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_category_id
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_list_normal_purchase
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_invoice_asset_category
msgid "Asset Category"
msgstr "資產類別"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Asset Durations to Modify"
msgstr "要修改的資產持續時間"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_end_date
msgid "Asset End Date"
msgstr "資產結束日"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__asset_method_time
msgid "Asset Method Time"
msgstr "資產方法時間"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__name
msgid "Asset Name"
msgstr "資產名稱"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_start_date
msgid "Asset Start Date"
msgstr "資產開始日"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__name
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__asset_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Asset Type"
msgstr " 資產類型 "

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_category
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__asset_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_tree
msgid "Asset category"
msgstr "資產類別"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Asset created"
msgstr "資產創建"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_depreciation_line
msgid "Asset depreciation line"
msgstr "資產折舊明細"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Asset sold or disposed. Accounting entry awaiting for validation."
msgstr "資產出售或處置。記帳分錄待確認。"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_asset_asset
msgid "Asset/Revenue Recognition"
msgstr "資產/收入確認"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_account_asset_asset_form
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_ids
#: model:ir.ui.menu,name:om_account_asset.menu_action_account_asset_asset_form
#: model:ir.ui.menu,name:om_account_asset.menu_action_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Assets"
msgstr "資產"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_asset_report
#: model:ir.model,name:om_account_asset.model_asset_asset_report
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_graph
#: model_terms:ir.ui.view,arch_db:om_account_asset.action_account_asset_report_pivot
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets Analysis"
msgstr "資產分析"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_bank_statement_line__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_move__asset_depreciation_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_payment__asset_depreciation_ids
msgid "Assets Depreciation Lines"
msgstr "資產折舊明細"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "處於報廢狀態的資產"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "處於草稿或打開狀態的資產"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in draft state"
msgstr "草稿資產"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Assets in running state"
msgstr "處於運行中狀態的資產"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_attachment_count
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_attachment_count
msgid "Attachment Count"
msgstr "附件數"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__open_asset
msgid "Auto-Confirm Assets"
msgstr "自動確認資產"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__last_day_period
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__last_day_period
msgid "Based on Last Day of Purchase Period"
msgstr "啟動於購買日當期的最後一天"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Cancel"
msgstr "取消"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Category"
msgstr "類別"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Category of asset"
msgstr "資產類別"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__open_asset
msgid ""
"Check this if you want to automatically confirm the assets of this category "
"when created by invoices."
msgstr ""
"如果您想讓這類固定資產在建立應付款單之後自動確認為「運行」狀態，勾選這裡。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__group_entries
msgid "Check this if you want to group the generated entries by categories."
msgstr "如果要按類別對生成的條目進行分組，請選中此項。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Linear: Calculated on basis of: Gross Value / Number of Depreciations\n"
"  * Degressive: Calculated on basis of: Residual Value * Degressive Factor"
msgstr ""
"選擇用於計算折舊行金額的方法。\n"
"  * 線性：根據以下公式計算：總價值/折舊次數\n"
"  * 遞減：根據以下公式計算：殘值 * 遞減係數"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_time
msgid ""
"Choose the method to use to compute the dates and number of entries.\n"
"  * Number of Entries: Fix the number of entries and the time between 2 "
"depreciations.\n"
"  * Ending Date: Choose the time between 2 depreciations and the date the "
"depreciations won't go beyond."
msgstr ""
"選擇用於計算日期和條目數的方法。\n"
"  * 條目數：固定條目數和 2 次折舊之間的時間。\n"
"  * 結束日期：選擇 2 次折舊和不超過折舊日期之間的時間。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_asset_depreciation_confirmation_wizard__date
msgid ""
"Choose the period for which you want to automatically post the depreciation "
"lines of running assets"
msgstr "選擇您要讓系統自動計算利息項目的期間"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__close
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__close
msgid "Close"
msgstr "關閉"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Closed"
msgstr "已關閉"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__company_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__company_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Company"
msgstr "公司"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method
msgid "Computation Method"
msgstr "計算方法"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Compute Asset"
msgstr "計算資產"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Compute Depreciation"
msgstr "計算折舊"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Confirm"
msgstr "確認"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Asset Moves"
msgstr "已建立資產分錄"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/wizard/asset_depreciation_confirmation_wizard.py:0
#, python-format
msgid "Created Revenue Moves"
msgstr "已建立收入分錄"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "建立者"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__create_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "建立於"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciated_value
msgid "Cumulative Depreciation"
msgstr "累計折舊"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__currency_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__currency_id
#, python-format
msgid "Currency"
msgstr "幣別"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Current"
msgstr "未到期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__amount
msgid "Current Depreciation"
msgstr "當前折舊"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__date
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Date"
msgstr "日期"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Date of asset"
msgstr "資產日期"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of asset purchase"
msgstr "資產購買日期"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Date of depreciation"
msgstr "折舊日期"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Account"
msgstr "遞延收入科目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_product_product__deferred_revenue_category_id
#: model:ir.model.fields,field_description:om_account_asset.field_product_template__deferred_revenue_category_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Deferred Revenue Type"
msgstr "遞延收入類型"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Deferred Revenues"
msgstr "遞延收入"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__degressive
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__degressive
msgid "Degressive"
msgstr "遞減"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_progress_factor
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_progress_factor
msgid "Degressive Factor"
msgstr "遞減因子"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation"
msgstr "折舊"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Board"
msgstr "折舊板"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_nbr
msgid "Depreciation Count"
msgstr "折舊期數"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__depreciation_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__depreciation_date
msgid "Depreciation Date"
msgstr "折舊日期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__date_first_depreciation
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__date_first_depreciation
msgid "Depreciation Dates"
msgstr "折舊日期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_id
msgid "Depreciation Entries: Asset Account"
msgstr "累積折舊項目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__account_depreciation_expense_id
msgid "Depreciation Entries: Expense Account"
msgstr "折舊分錄:費用項目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_id
msgid "Depreciation Entry"
msgstr "折舊分錄"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Information"
msgstr "折舊資訊"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__depreciation_line_ids
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Depreciation Lines"
msgstr "折舊明細"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Depreciation Method"
msgstr "折舊方法"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Depreciation Month"
msgstr "折舊月份"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__name
msgid "Depreciation Name"
msgstr "折舊名稱"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/wizard/asset_modify.py:0
#, python-format
msgid "Depreciation board modified"
msgstr "折舊版被修改"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Depreciation line posted."
msgstr "已過帳折舊明細。"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__display_name
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Move"
msgstr "處置移動"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Disposal Moves"
msgstr "處置移動"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "Document closed."
msgstr "文件已關閉"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__draft
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__draft
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Draft"
msgstr "草稿"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__end
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__end
msgid "Ending Date"
msgstr "期末日期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_end
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_end
msgid "Ending date"
msgstr "結束日期"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Extended Filters..."
msgstr "擴展篩選..."

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid "First Depreciation Date"
msgstr "第一個折舊日期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_follower_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_follower_ids
msgid "Followers"
msgstr "關注者"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_partner_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_partner_ids
msgid "Followers (Partners)"
msgstr "訂閱者(合作夥伴)"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_type_icon
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid ""
"From this report, you can have an overview on all depreciations. The\n"
"            search bar can also be used to personalize your assets "
"depreciation reporting."
msgstr ""
"從本報告中，您可以全面瞭解所有折舊情況。這\n"
"            搜索欄還可用於個人化您的資產折舊報告。"

#. module: om_account_asset
#: model:ir.ui.menu,name:om_account_asset.menu_asset_depreciation_confirmation_wizard
msgid "Generate Assets Entries"
msgstr "產生折舊分錄"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid "Generate Entries"
msgstr "產生記項"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__gross_value
msgid "Gross Amount"
msgstr "總金額"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value
msgid "Gross Value"
msgstr "資產總值"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Gross value of asset"
msgstr "資產總值"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Group By"
msgstr "分組按"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_search
msgid "Group By..."
msgstr "分組依據"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__group_entries
msgid "Group Journal Entries"
msgstr "日記帳分錄分組"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__has_message
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_exception_icon
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_exception_icon
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_sms_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_has_error
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the asset date (purchase date) instead of the first January / Start "
"date of fiscal year"
msgstr ""
"表示此資產的第一個折舊條目必須從資產日期（購買日期）而不是財政年度的第一個 1 "
"月 /開始日期完成"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__prorata
msgid ""
"Indicates that the first depreciation entry for this asset have to be done "
"from the purchase date instead of the first of January"
msgstr "表示此資產的第一個折舊條目必須從購買日期而不是 1 月 1 日起完成"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__installment_nbr
msgid "Installment Count"
msgstr "遞延收入計算"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__invoice_id
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Invoice"
msgstr "應收憑單"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_is_follower
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_is_follower
msgid "Is Follower"
msgstr "是訂閱者"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__salvage_value
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "您計劃的量, 不可以折舊。"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Items"
msgstr "項目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__journal_id
msgid "Journal"
msgstr "日記帳"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
#, python-format
msgid "Journal Entries"
msgstr "日記帳分錄"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move
msgid "Journal Entry"
msgstr "日記帳分錄"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_account_move_line
msgid "Journal Item"
msgstr "日記帳項目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_uid
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "最後更新人"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_depreciation_confirmation_wizard__write_date
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "最後更新時間"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method__linear
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method__linear
msgid "Linear"
msgstr "線性"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_check
msgid "Linked"
msgstr "分錄狀況"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__date_first_depreciation__manual
msgid "Manual"
msgstr "手動"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__date_first_depreciation__manual
msgid "Manual (Defaulted on Purchase Date)"
msgstr "手動(預設在購買日期)"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_ids
msgid "Messages"
msgstr "訊息"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify"
msgstr "修改"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_modify
#: model:ir.model,name:om_account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "修改資產"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Modify Depreciation"
msgstr "修改折舊"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_move_line__asset_mrr
msgid "Monthly Recurring Revenue"
msgstr "每月遞延收入"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__my_activity_date_deadline
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_calendar_event_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_date_deadline
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_summary
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_type_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__remaining_value
msgid "Next Period Depreciation"
msgstr "下一期折舊"

#. module: om_account_asset
#: model_terms:ir.actions.act_window,help:om_account_asset.action_asset_asset_report
msgid "No content"
msgstr "無內容"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__note
msgid "Note"
msgstr "註記"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__first_depreciation_manual_date
msgid ""
"Note that this date does not alter the computation of the first journal "
"entry in case of prorata temporis assets. It simply changes its accounting "
"date"
msgstr ""
"請注意，此日期不會改變第一個日誌條目的計算，以防質子時間資產。它只是更改其會"
"計日期"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_needaction_counter
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_needaction_counter
msgid "Number of Actions"
msgstr "動作數"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_number
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_number
msgid "Number of Depreciations"
msgstr "折舊數量"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__method_time__number
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__method_time__number
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Number of Entries"
msgstr "攤折次數"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_period
msgid "Number of Months in a Period"
msgstr "在一個期間內的月數"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_error_counter
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_needaction_counter
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要執行操作的訊息數量"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__message_has_error_counter
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "One Entry Every"
msgstr "每次攤折期間"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__partner_id
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__partner_id
#, python-format
msgid "Partner"
msgstr "合作夥伴"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_period
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__method_period
msgid "Period Length"
msgstr "期間長度"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Periodicity"
msgstr "週期"

#. module: om_account_asset
#: model:ir.actions.act_window,name:om_account_asset.action_asset_depreciation_confirmation_wizard
msgid "Post Depreciation Lines"
msgstr "折舊明細帳"

#. module: om_account_asset
#. odoo-javascript
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__move_posted_check
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__move_check
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
#, python-format
msgid "Posted"
msgstr "兌現"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__posted_value
msgid "Posted Amount"
msgstr "已過帳金額"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Posted depreciation lines"
msgstr "已過帳折舊明細"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_product_template
msgid "Product"
msgstr "產品"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__prorata
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__prorata
msgid "Prorata Temporis"
msgstr "即時按比例分配"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid ""
"Prorata temporis can be applied only for the \"number of depreciations\" "
"time method."
msgstr "Prorata temporis 僅可使用在時間方法為 \"依折舊數量\" ."

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Purchase"
msgstr "採購"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Purchase Month"
msgstr "採購月份"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__purchase
msgid "Purchase: Asset"
msgstr "採購:資產"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__rating_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__rating_ids
msgid "Ratings"
msgstr "評分"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_modify__name
msgid "Reason"
msgstr "原因"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Account"
msgstr "遞延收入會計項目"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Recognition Income Account"
msgstr "遞延收入沖帳會計項目"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__code
msgid "Reference"
msgstr "參考"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Residual"
msgstr "殘值"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__value_residual
msgid "Residual Value"
msgstr "待攤銷餘額"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__activity_user_id
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__activity_user_id
msgid "Responsible User"
msgstr "負責人"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_asset__state__open
#: model:ir.model.fields.selection,name:om_account_asset.selection__asset_asset_report__state__open
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_asset_report_search
msgid "Running"
msgstr "運行中"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__message_has_sms_error
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: om_account_asset
#: model:ir.model.fields.selection,name:om_account_asset.selection__account_asset_category__type__sale
msgid "Sale: Revenue Recognition"
msgstr "銷售:收入確認"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Sales"
msgstr "銷售"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__salvage_value
msgid "Salvage Value"
msgstr "資產殘值"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Search Asset Category"
msgstr "搜索資產類別"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Sell or Dispose"
msgstr "銷售或棄置"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__sequence
msgid "Sequence"
msgstr "序列"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "Set to Draft"
msgstr "設為草稿"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_period
msgid "State here the time between 2 depreciations, in months"
msgstr "在此處說明夾在兩次折舊之間的時間，已月數輸入"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid "State of Asset"
msgstr "資產狀態"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__state
msgid "Status"
msgstr "狀態"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_state
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期: 已經超過截止日期 \n"
" 現今: 活動日期是當天 \n"
" 計劃: 未來活動."

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_period
msgid "The amount of time between two depreciations, in months"
msgstr "折舊期間的時間量(以月形式)"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/wizard/asset_modify.py:0
#, python-format
msgid ""
"The number of depreciations must be greater than the number of posted or "
"draft entries to allow for complete depreciation of the asset."
msgstr "折舊次數必須大於過帳或草稿條目的數量，以允許資產完全折舊。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__method_number
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "需要折舊您的資產的折舊數量"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid ""
"The number of depreciations or the period length of your asset category "
"cannot be 0."
msgstr "您的資產類別的折舊次數或期間長度不能為 0。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be "
"based on the last day of the purchase month or the purchase year (depending "
"on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the "
"purchase date."
msgstr ""
"計算第一次折舊日期的方法。\n"
"  * 基於購買期的最後一天：折舊日期將基於購買月或購買年的最後一天（取決於折舊"
"的周期）。\n"
"  * 基於購買日期：折舊日期將基於購買日期。"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__date_first_depreciation
msgid ""
"The way to compute the date of the first depreciation.\n"
"  * Based on last day of purchase period: The depreciation dates will be "
"based on the last day of the purchase month or the purchase year (depending "
"on the periodicity of the depreciations).\n"
"  * Based on purchase date: The depreciation dates will be based on the "
"purchase date.\n"
msgstr ""
"計算第一次折舊日期的方法。\n"
"  * 基於購買期的最後一天：折舊日期將基於購買月或購買年的最後一天（取決於折舊"
"的周期）。\n"
"  * 基於購買日期：折舊日期將基於購買日期。\n"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid ""
"This depreciation is already linked to a journal entry. Please post or "
"delete it."
msgstr "此折舊明細已連結到日記帳分錄。請進行過帳或刪除。"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_asset_depreciation_confirmation_wizard
msgid ""
"This wizard will post installment/depreciation lines for the selected month."
"<br/>\n"
"                        This will generate journal entries for all related "
"installment lines on this period\n"
"                        of asset/revenue recognition as well."
msgstr ""
"此動作將過帳所選月份的遞延收入/折舊明細。<br/>這將生成此資產/遞延收入確認期間"
"所有相關攤銷明細的日記帳分錄。"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__method_time
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__method_time
msgid "Time Method"
msgstr "時間運作方式"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "Time Method Based On"
msgstr "時間運作方式基於"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__type
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__type
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_search
msgid "Type"
msgstr "類型"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__activity_exception_decoration
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型."

#. module: om_account_asset
#. odoo-javascript
#: code:addons/om_account_asset/static/src/js/account_asset.js:0
#, python-format
msgid "Unposted"
msgstr "未過帳"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__unposted_value
msgid "Unposted Amount"
msgstr "未過帳金額"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_purchase_tree
msgid "Vendor"
msgstr "供應商"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "Vendor bill cancelled."
msgstr "供應商賬單已取消。"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_asset__website_message_ids
#: model:ir.model.fields,field_description:om_account_asset.field_account_asset_category__website_message_ids
msgid "Website Messages"
msgstr "網站訊息"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__website_message_ids
#: model:ir.model.fields,help:om_account_asset.field_account_asset_category__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: om_account_asset
#: model:ir.model.fields,help:om_account_asset.field_account_asset_asset__state
#: model:ir.model.fields,help:om_account_asset.field_account_asset_depreciation_line__parent_state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation "
"lines can be posted in the accounting.\n"
"You can manually close an asset when the depreciation is over. If the last "
"line of depreciation is posted, the asset automatically goes in that status."
msgstr ""
"創建資產時,狀態為\"草稿\"。\n"
"如果確認資產,狀態將處於\"正在運行\"狀態,折舊明細可以過帳到會計中。\n"
"折舊結束后,您可以手動關閉資產。當您過帳最後的折舊明細時,資產將自動進入關閉狀"
"態。"

#. module: om_account_asset
#: model:ir.model.fields,field_description:om_account_asset.field_asset_asset_report__name
msgid "Year"
msgstr "年"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that contains posted entries."
msgstr "您不能刪除包含已過帳項目的文件。"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete a document that is in %s state."
msgstr "在 %s狀態, 您不能刪除文檔。"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted depreciation lines."
msgstr "您不能刪除已過帳的折舊明細。"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_asset.py:0
#, python-format
msgid "You cannot delete posted installment lines."
msgstr "您不能刪除已過帳的遞延收入明細。"

#. module: om_account_asset
#. odoo-python
#: code:addons/om_account_asset/models/account_move.py:0
#, python-format
msgid "You cannot reset to draft for an entry having a posted asset"
msgstr "您不能為具有已過帳資產的條目重置為草稿"

#. module: om_account_asset
#: model:ir.model,name:om_account_asset.model_asset_depreciation_confirmation_wizard
msgid "asset.depreciation.confirmation.wizard"
msgstr "資產折舊確認嚮導"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "e.g. Computers"
msgstr "例如：電腦"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_asset_form
msgid "e.g. Laptop iBook"
msgstr "例如: Laptop iBook 筆記本電腦"

#. module: om_account_asset
#: model_terms:ir.ui.view,arch_db:om_account_asset.asset_modify_form
#: model_terms:ir.ui.view,arch_db:om_account_asset.view_account_asset_category_form
msgid "months"
msgstr "月"
