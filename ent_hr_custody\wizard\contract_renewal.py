# -*- coding: utf-8 -*-
###############################################################################
#
#    A part of Open HRMS Project <https://www.openhrms.com>
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>)
#    Author: Cybrosys Techno Solutions (<EMAIL>)
#
#    This program is under the terms of the Odoo Proprietary
#    License v1.0 (OPL-1)
#    It is forbidden to publish, distribute, sublicense, or sell copies
#    of the Software
#    or modified copies of the Software.
#
#    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
#    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
#    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
#    IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
#    DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
#    TORT OR OTHERWISE,
#    ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
#    DEALINGS IN THE SOFTWARE.
#
###############################################################################
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError


class ContractRenewal(models.TransientModel):
    """Hr custody contract renewal wizard"""
    _name = 'contract.renewal'
    _description = 'Contract Renewal'

    returned_date = fields.Date(string='Renewal Date', required=1)

    @api.constrains('returned_date')
    def validate_return_date(self):
        """Function for renewal date validation"""
        context = self._context
        custody_obj = self.env['hr.custody'].search(
            [('id', '=', context.get('custody_id'))])
        if self.returned_date <= custody_obj.date_request:
            raise ValidationError('Please Give Valid Renewal Date')

    def proceed(self):
        """Button for renewal date validate """
        context = self._context
        custody_obj = self.env['hr.custody'].search(
            [('id', '=', context.get('custody_id'))])
        custody_obj.write({'renew_return_date': True,
                           'renew_date': self.returned_date,
                           'state': 'to_approve'})
