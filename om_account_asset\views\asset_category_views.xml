<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_account_asset_category_form" model="ir.ui.view">
        <field name="name">account.asset.category.form</field>
        <field name="model">account.asset.category</field>
        <field name="arch" type="xml">
            <form string="Asset category">
                <sheet>
                    <div class="oe_title">
                        <label for="name" string="Asset Type" class="oe_edit_only"
                                invisible="type != 'purchase'"/>
                        <label for="name" string="Deferred Revenue Type" class="oe_edit_only"
                               invisible="type == 'purchase'"/>
                        <h1>
                            <field name="name" placeholder="e.g. Computers"/>
                        </h1>
                    </div>
                    <group string="Journal Entries">
                        <group>
                            <field name="journal_id"/>
                            <div>
                                <label for="account_asset_id"  invisible="type != 'purchase'"
                                       style="font-weight: bold" class="o_light_label"/>
                                <label for="account_asset_id" string="Deferred Revenue Account"
                                       invisible="type != 'sale'" style="font-weight: bold"
                                       class="o_light_label"/>
                            </div>
                            <field name="account_asset_id" nolabel="1"
                                   domain="[('company_id', '=', company_id)]"/>
                            <div>
                                <label for="account_depreciation_id"  invisible="type != 'purchase'"
                                       style="font-weight: bold" class="o_light_label"/>
                                <label for="account_depreciation_id" string="Recognition Income Account"
                                       invisible="type != 'sale'"
                                       style="font-weight: bold" class="o_light_label"/>
                            </div>
                            <field name="account_depreciation_id" nolabel="1"
                                   domain="[('company_id', '=', company_id)]"/>
                        </group>
                        <group>
                            <div>
                                <label for="account_depreciation_expense_id"
                                       invisible="type != 'purchase'"
                                       style="font-weight: bold" class="o_light_label"/>
                                <label for="account_depreciation_expense_id" string="Recognition Account"
                                       invisible="type != 'sale'"
                                       style="font-weight: bold" class="o_light_label"/>
                            </div>
                            <field name="account_depreciation_expense_id" nolabel="1"
                                   domain="[('company_id', '=', company_id)]"/>
                            <field name="account_analytic_id" domain="[('company_id', '=', company_id)]"
                                   groups="analytic.group_analytic_accounting"/>
                            <field name="analytic_distribution" widget="analytic_distribution"/>
                        </group>
                    </group>
                    <group>
                        <group string="Periodicity">
                            <field name="method_time" string="Time Method Based On" widget="radio"
                                    invisible="type != 'purchase'"/>
                            <field name="method_number" string="Number of Entries"
                                   required="method_time == 'number'"
                                   invisible="method_time != 'number' or type == False"/>
                            <label for="method_period" string="One Entry Every"/>
                            <div>
                                <field name="method_period" nolabel="1"
                                       invisible="type == False" class="oe_inline"/>
                                months
                            </div>
                            <field name="method_end"
                                   required="method_time == 'end'"
                                   invisible="method_time != 'end'"/>
                        </group>
                        <group>
                            <field name="type" invisible="1"/>
                            <field name="company_id" invisible="1"/>
                            <field name="company_id" options="{'no_create': True}"
                                   groups="base.group_multi_company"/>
                        </group>
                        <group string="Additional Options">
                            <field name="open_asset"/>
                            <field name="group_entries"/>
                            <field name="date_first_depreciation"/>
                        </group>
                        <group invisible="type == 'sale'" string="Depreciation Method">
                            <field name="method" widget="radio"/>
                            <field name="method_progress_factor"
                                   invisible="method_time == 'linear'" required="method == 'degressive'"/>
                            <field name="prorata" invisible="method_time == 'end'"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_account_asset_asset_category_kanban" model="ir.ui.view">
        <field name="name">account.asset.category.kanban</field>
        <field name="model">account.asset.category</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="journal_id"/>
                <field name="method"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_card oe_kanban_global_click">
                            <div class="row mb4">
                                <div class="col-6">
                                    <strong>
                                        <span>
                                            <t t-esc="record.name.value"/>
                                        </span>
                                    </strong>
                                </div>
                                <div class="col-6 text-end">
                                    <span class="badge badge-pill">
                                        <strong>
                                            <t t-esc="record.method.value"/>
                                        </strong>
                                    </span>
                                </div>
                            </div>
                            <div>
                                <t t-esc="record.journal_id.value"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_account_asset_category_tree" model="ir.ui.view">
        <field name="name">account.asset.category.tree</field>
        <field name="model">account.asset.category</field>
        <field name="arch" type="xml">
            <tree string="Asset category">
                <field name="name"/>
                <field name="journal_id"/>
                <field name="method"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_account_asset_category_search" model="ir.ui.view">
        <field name="name">account.asset.category.search</field>
        <field name="model">account.asset.category</field>
        <field name="arch" type="xml">
            <search string="Search Asset Category">
                <filter string="Sales" name="sales" domain="[('type','=', 'sale')]" help="Deferred Revenues"/>
                <filter string="Purchase" name="purchase" domain="[('type','=', 'purchase')]" help="Assets"/>
                <field name="name" string="Category"/>
                <field name="journal_id"/>
                <group expand="0" string="Group By...">
                    <filter string="Type" name="type" domain="[]" context="{'group_by':'type'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_account_asset_asset_list_normal_purchase" model="ir.actions.act_window">
        <field name="name">Asset Category</field>
        <field name="res_model">account.asset.category</field>
        <field name="domain">[('type', '=', 'purchase')]</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="context">{'default_type': 'purchase'}</field>
    </record>

    <menuitem id="menu_action_account_asset_asset_list_normal_purchase"
              parent="account.account_management_menu"
              action="action_account_asset_asset_list_normal_purchase"
              sequence="6"/>

</odoo>
