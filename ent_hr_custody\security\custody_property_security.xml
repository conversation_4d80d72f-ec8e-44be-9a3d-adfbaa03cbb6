<?xml version="1.0" ?>
<odoo>
<!--    Rule for multi companies: model custody property-->
    <record id="property_rule_custody" model="ir.rule">
        <field name="name">Custody Multi Company</field>
        <field name="model_id" ref="model_custody_property"/>
        <field eval="True" name="global"/>
        <field name="domain_force">['|',('company_id','=',False),('company_id','child_of',[user.company_id.id])]</field>
    </record>
</odoo>
