<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--    Pivot view of report custody-->
    <record id="view_report_custody" model="ir.ui.view">
        <field name="name">report.custody.pivot</field>
        <field name="model">report.custody</field>
        <field name="arch" type="xml">
            <pivot string="Ticket Analysis" display_quantity="true"
                   disable_linking="True">
                <field name="name" type="row"/>
            </pivot>
        </field>
    </record>
    <!--Action for report custody-->
    <record id="report_custody_action" model="ir.actions.act_window">
        <field name="name">Custody Analysis</field>
        <field name="res_model">report.custody</field>
        <field name="view_mode">pivot</field>
        <field name="context">{'group_by_no_leaf':1,'group_by':[]}</field>
        <field name="help">This report allows you to analyse all Custody
            Requests.
        </field>
    </record>
    <!--Menu for report-->
    <menuitem name="Report" id="custody_report_menu_root"
              groups="hr.group_hr_manager"
              parent="ent_hr_custody.hr_custody_main_menu" sequence="1"/>
    <!--Menu for custody analysis-->
    <menuitem name="Custody Analysis"
              action="report_custody_action"
              id="custody_report_menu_do_stuff"
              groups="hr.group_hr_manager"
              parent="custody_report_menu_root" sequence="1"/>
</odoo>
