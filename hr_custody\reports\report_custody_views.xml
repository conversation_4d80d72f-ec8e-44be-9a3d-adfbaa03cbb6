<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- Report Custody Pivot view -->
        <record id="report_custody_view_pivot" model="ir.ui.view">
            <field name="name">report.custody.view.pivot</field>
            <field name="model">report.custody</field>
            <field name="arch" type="xml">
                <pivot string="Ticket Analysis" display_quantity="true" disable_linking="True">
                    <field name="name" type="row"/>
                </pivot>
            </field>
        </record>
        <!-- Report Custody action -->
        <record id="report_custody_action" model="ir.actions.act_window">
            <field name="name">Custody Analysis</field>
            <field name="res_model">report.custody</field>
            <field name="view_mode">pivot</field>
            <field name="context">{'group_by_no_leaf':1,'group_by':[]}</field>
            <field name="help">This report allows you to analyse all Custody Requests.</field>
        </record>
        <!-- Report Custody menu-->
        <menuitem name="Report" id="custody_report_menu"
                  groups="hr.group_hr_manager"
                  parent="hr_custody_main_menu" sequence="1"/>

        <menuitem name="Custody Analysis" action="report_custody_action" id="custody_report_menu_custody_analysis"
                  groups="hr.group_hr_manager"
                  parent="custody_report_menu" sequence="1"/>
</odoo>
